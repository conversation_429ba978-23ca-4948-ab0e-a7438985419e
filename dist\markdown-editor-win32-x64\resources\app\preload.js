const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Menu actions
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-new-file', callback);
    ipcRenderer.on('menu-open-file', callback);
    ipcRenderer.on('menu-save-file', callback);
    ipcRenderer.on('menu-save-as', callback);
    ipcRenderer.on('menu-export-html', callback);
    ipcRenderer.on('menu-export-pdf', callback);
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// Expose app information
contextBridge.exposeInMainWorld('appInfo', {
  isElectron: true,
  platform: process.platform,
  version: process.versions.electron
});
